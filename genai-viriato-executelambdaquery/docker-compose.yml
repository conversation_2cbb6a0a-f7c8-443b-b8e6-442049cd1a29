services:
  localstack:
    image: localstack/localstack:latest
    container_name: localstack
    # network_mode: bridge
    environment:
      - AWS_DEFAULT_REGION=eu-west-1
      - EDGE_PORT=4566
      - SERVICES=cloudwatch
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
    ports:
      - 4566:4566
    volumes:
      - "${LOCALSTACK_VOLUME_DIR:-./volume}:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
    networks:
      - genai-net
  executeLambdaQuery:
    image: executeLambdaQuery:0.1.0
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - AWS_REGION=eu-west-1
      - OPENTELEMETRY_COLLECTOR_CONFIG_FILE=/var/task/collector_local.yaml
    platform: linux/amd64
    ports:
      - 9000:8080
    networks:
      - genai-net



networks:
  genai-net: