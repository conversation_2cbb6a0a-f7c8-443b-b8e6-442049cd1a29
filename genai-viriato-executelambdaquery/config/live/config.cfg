COMPONENT_NAME=executeLambdaQuery
COMPONENT_DESCRIPTION=Translates natural language requests to queries
COMPONENT_VERSION=0.1.0
PROMPT_REPOSITORY_ROUTE=./resources/templates/
S3_BUCKET=



# framework configurations
    

# integrations configurations
OPENAI_ENDPOINT=
                        
PROXY_NG=
                        
OPENAI_KEY=
                        
OPENAI_VERSION=
                        
TIKTOKEN_CACHE_DIR=./tiktoken_cache
                        
DEFAULT_EMBEDDING_MODEL_NAME=text-embedding-ada-002
                        
DEFAULT_LLM_MODEL_NAME=gpt-35-turbo
                        
INSTRUCTIONS=
                        
TEMPERATURE=0.5
                        
MAX_TOKENS=4000
                        
OPENAI_TIMEOUT=60
                        
OPENAI_MAX_RETRIES=3
                        