from __future__ import annotations

import abc
from typing import Any

from genai.libs.observability import trace_execution


class AbstractEventUnitOfWork(abc.ABC):
    _embedding: Any

    def __enter__(self) -> AbstractEventUnitOfWork:
        return self

    def __exit__(self, *args):
        """Subclasses should implement any necessary cleanup logic here."""

    @abc.abstractmethod
    def get(self):
        raise NotImplementedError

    @abc.abstractmethod
    @trace_execution
    def send_event(self, message: dict, partition_key: str):
        raise NotImplementedError
