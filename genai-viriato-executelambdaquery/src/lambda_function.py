import boto3
import json
import logging
import os
import re

from genai.libs.observability.genai_lambda import trace_lambda
from src.infrastructure.core.config import genai_config
from openai import AzureOpenAI

# please do not remove, this preconfigures opentelemetry!!
default_dimensions = {
    "Lambda": genai_config.GENAI_NAME,
    "Application": genai_config.GENAI_APPLICATION,
    "Product": genai_config.GENAI_PRODUCT,
    "service.name": genai_config.GENAI_NAME,
}

# Logger config
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Function to get secrets from the Secrets Manager
SEC_PROXY_URL = "work/PROXY_NG"
SEC_OPENAI_ENDPOINT_NAME = "work/OPENAI_ENDPOINT"
SEC_OPENAI_KEY_NAME = "work/OPENAI_KEY"


def get_secret(secret_name):
    clientsec = boto3.client("secretsmanager")
    response = clientsec.get_secret_value(SecretId=secret_name)
    secret_str = response["SecretString"]
    return secret_str


oai_endpoint = get_secret(SEC_OPENAI_ENDPOINT_NAME)
oai_key = get_secret(SEC_OPENAI_KEY_NAME)
logger.info(f"Endpoint: {oai_endpoint}")

# Requests config to globally use the proxy
PROXY_URL = get_secret(SEC_PROXY_URL)
proxies = {"http": PROXY_URL, "https": PROXY_URL}
os.environ["HTTP_PROXY"] = PROXY_URL
os.environ["HTTPS_PROXY"] = PROXY_URL

# Init Azure OpenAI model
client = AzureOpenAI(
  azure_endpoint=oai_endpoint,
  api_key=oai_key,
  api_version="2024-10-21",
  timeout=120,  # 2 minutes in seconds
  max_retries=3
)

with open('resources/commun_concepts.txt', 'r', encoding='utf-8') as file:
    commun_concepts = file.read().strip()

with open('resources/prompt_sql.txt', 'r') as file:
    prompt_sql = file.read().strip()

with open('resources/prompts_sql_db.txt', 'r') as file:
    prompt_sql_db = file.read().strip()


# Function to generate response
def generate_response(client, prompt, commun_concepts, natural_language):
    combined_prompt = commun_concepts + "\n" + prompt
    response = client.chat.completions.create(
        model="viriato-iac-llm-gpt4o-iac",
        messages=[
            {"role": "system", "content": combined_prompt},
            {"role": "user", "content": natural_language}
        ],
        max_tokens=2000
    )

    llm_answer = response.choices[0].message.content
    return llm_answer


# Function to extract text and sql
def extract_text_and_sql(response_text):
    text = ""
    sql_query = ""

    try:
        response_dict = json.loads(response_text)
        text = response_dict.get("resultText", "")
        sql_query = response_dict.get("query", "")

    except json.JSONDecodeError:
        logger.info("JSON decode failed, attempting regex extraction")
        # JSON decode failed. Use regex as fallback
        text_match = re.search(r"text:\s*(.+?),\s*sql:", response_text)
        sql_match = re.search(r"query:\s*(.+)", response_text)

        if text_match and sql_match:
            text = text_match.group(1).strip()
            sql_query = sql_match.group(1).strip()

            # Clean up the extracted strings: remove leading/trailing whitespace and quotes
            text = text.strip('"').strip("'")
            sql_query = sql_query.strip('"').strip("'")

        else:
            text = ""
            sql_query = ""

    return text, sql_query, response_dict


# Function to handle sql query
def handle_sql_query(natural_language_query):
    full_prompt_sql = prompt_sql + "\n" + prompt_sql_db
    response_text = generate_response(client, full_prompt_sql, commun_concepts, natural_language_query)
    text, sql_query, response_dict = extract_text_and_sql(response_text)
    if sql_query and 'SELECT' in sql_query.upper():
        return response_dict
    else:
        return {}


# Lambda handler
@trace_lambda(
    environment=genai_config.GENAI_ENVIRONMENT,
    default_dimensions=default_dimensions,
)
def handler(event, context):
    """
    AWS Lambda that invokes Azure OpenAI to classify the user's request.
    """
    natural_language = event.get("message")

    if not natural_language:
        raise ValueError("Error: No message provided")

    # Short-circuit for CICD test
    if natural_language.strip().lower() == "testing lambda in cicd":
        return {
            "message": "Test successfully executed."
        }

    try:
        response_dict = handle_sql_query(natural_language)
        return response_dict
    except Exception as e:
        raise RuntimeError(f"Error during handle_sql_query call: {e}") from e
