import os

import pytest
from jinja2 import TemplateNotFound

from src.infrastructure.genai.prompt import PromptJinjaRepository


@pytest.fixture
def temp_prompt_template(tmpdir):
    """Fixture to create a temporary prompt_template_temp.txt file."""

    content = (
        '[*% if use_case == "summarization" %}'
        '{"role": "system", "content": "Summarize the following text"},'
        "*% else %}"
        '{"role": "system", "content": "You respond to users queries"},'
        "*% endif %}"
        '{"role": "user", "content": "{* query }}"}]'
    )

    content = content.replace("*", "{")

    file_path = os.path.join(tmpdir, "prompt_template.txt")

    with open(file_path, "w") as file:
        file.write(content)

    yield file_path
    os.remove(file_path)


def test_get_prompt(temp_prompt_template):
    """Test that the get_prompt method processes the template correctly."""

    template = "prompt_template.txt"
    params = {"use_case": "summarization", "query": "Hello"}
    repo = PromptJinjaRepository(route=os.path.dirname(temp_prompt_template))

    with repo:
        output = repo.get_prompt(template, params)

    expected_output = (
        '[{"role": "system", "content": "Summarize the following text"},'
        '{"role": "user", "content": "Hello"}]'
    )

    assert output.strip() == expected_output.strip()


def test_get_prompt_not_found():
    """Test that the get_prompt with a template that
    does not exist raises an error."""

    repo = PromptJinjaRepository(route="./resources/templates")

    template = "prompt_template_not_found.txt"
    params = {"use_case": "summarization", "query": "Hello"}

    with pytest.raises(TemplateNotFound) as exception_info:
        with repo:
            repo.get_prompt(template, params)

    assert "prompt_template_not_found.txt" in str(exception_info.value)
