from src.domain import DomainException


class TestDomainError:

    def test_init(self):
        """Ensure DomainError initializes with the correct message."""

        message = "Error en el dominio"
        error = DomainException(message)
        assert str(error) == message

    def test_srt(self):
        """Ensure DomainError initializes with the correct message."""

        message = "Error en el dominio"
        error = DomainException(message)
        assert str(error) == message
