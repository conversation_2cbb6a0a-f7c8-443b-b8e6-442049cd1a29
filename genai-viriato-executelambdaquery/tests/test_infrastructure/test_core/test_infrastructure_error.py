from src.infrastructure import InfrastructureException


class TestInfraError:

    def test_init(self):
        """Ensure InfrastructureError initializes with the correct message."""

        message = "Error en el infraestructura"
        error = InfrastructureException(message)
        assert str(error) == message

    def test_srt(self):
        """Ensure string representation of InfrastructureError is correct."""

        message = "Error en el infraestructura"
        error = InfrastructureException(message)
        assert str(error) == message
