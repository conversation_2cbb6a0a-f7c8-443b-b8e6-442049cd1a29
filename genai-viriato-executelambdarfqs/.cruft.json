{"template": "https://bitbucket.globaldevtools.bbva.com/bitbucket/scm/genai_viriato/genai-viriato-executelambdarfqs.git", "commit": "24aefae18b9ef9401698e03c748960d19108db3c", "checkout": null, "context": {"cookiecutter": {"application": "viriato", "project_slug": "executeLambdaRfqs", "component_description": "Converts BBG chats to formatted RFQs", "project_author_name": "AA&AT", "email": "<EMAIL>", "version": "0.1.0", "lambda_type": "orq", "integration_type": "API", "framework": "native", "integration": "openai", "_scaffold_version": "1.5.0", "_framework_values": {"langchain": {"dependencies": {"langchain-core": "0.3.30", "langchain-community": "0.3.14", "langchain-text-splitters": "0.3.5"}, "integrations": {"openai": {"langchain-openai": "0.2.7"}, "opensearch": {"requests-aws4auth": "1.2.3", "opensearch-py": "2.4.2", "tenacity": "8.3.0"}, "bedrock": {"langchain-aws": "0.2.7"}, "faiss": {"faiss-cpu": "1.8.0", "numpy": "1.26.4"}, "langgraph": {"langgraph": "0.2.59", "langgraph-checkpoint": "2.0.9", "langgraph-checkpoint-postgres": "2.0.7", "psycopg": "3.2.3", "psycopg-pool": "3.2.3", "psycopg-binary": "3.2.3"}}}, "native": {"dependencies": {}, "integrations": {"openai": {"openai": "1.28.1"}, "opensearch": {"requests-aws4auth": "1.2.3", "opensearch-py": "2.4.2", "tenacity": "8.3.0"}, "faiss": {"faiss-cpu": "1.8.0", "h5py": "3.10.0", "numpy": "1.26.4"}, "langgraph": {"langgraph": "0.2.59", "langgraph-checkpoint": "2.0.9", "langgraph-checkpoint-postgres": "2.0.7", "psycopg": "3.2.3", "psycopg-pool": "3.2.3", "psycopg-binary": "3.2.3"}}}}, "_configs": {"langchain": {"configurations": [], "integrations": {"openai": [{"name": "OPENAI_ENDPOINT", "type": "str", "value": ""}, {"name": "PROXY_NG", "type": "str", "value": ""}, {"name": "OPENAI_KEY", "type": "str", "value": ""}, {"name": "OPENAI_VERSION", "type": "str", "value": ""}, {"name": "TIKTOKEN_CACHE_DIR", "type": "str", "value": ""}, {"name": "DEFAULT_EMBEDDING_MODEL_NAME", "type": "str", "value": "text-embedding-ada-002"}, {"name": "DEFAULT_LLM_MODEL_NAME", "type": "str", "value": "gpt-35-turbo"}, {"name": "INSTRUCTIONS", "type": "str", "value": ""}, {"name": "TEMPERATURE", "type": "float", "value": 0.5}, {"name": "MAX_TOKENS", "type": "int", "value": 4000}, {"name": "OPENAI_TIMEOUT", "type": "int", "value": 60}, {"name": "OPENAI_MAX_RETRIES", "type": "int", "value": 3}], "opensearch": [{"name": "OPENSEARCH_REGION", "type": "str", "value": "eu-west-1"}, {"name": "OPENSEARCH_INDEX", "type": "str", "value": ""}, {"name": "OPENSEARCH_ENDPOINT", "type": "str", "value": ""}, {"name": "OPENSEARCH_TIMEOUT", "type": "int", "value": 60}, {"name": "OPENSEARCH_MAX_RETRIES", "type": "int", "value": 3}, {"name": "OPENSEARCH_WAIT_EXPONENTIAL_JITTER", "type": "bool", "value": true}, {"name": "OPENSEARCH_USER", "type": "str", "value": ""}, {"name": "OPENSEARCH_PASSWORD", "type": "str", "value": ""}], "dynamodb": [{"name": "AWS_REGION_NAME", "type": "str", "value": "eu-west-1"}], "faiss": [{"name": "FAISS_PATH", "type": "str", "value": "/tmp/faiss/"}, {"name": "FAISS_INDEX", "type": "str", "value": "findex"}, {"name": "FAISS_DIM", "type": "int", "value": 1536}, {"name": "FAISS_M", "type": "int", "value": 32}], "bedrock": [{"name": "BEDROCK_MODEL_ID", "type": "str", "value": ""}, {"name": "TIKTOKEN_CACHE_DIR", "type": "str", "value": ""}, {"name": "ALLOW_SYSTEM_PROMPT", "type": "list", "value": []}, {"name": "TEMPERATURE", "type": "float", "value": 0.5}, {"name": "MAX_TOKENS", "type": "int", "value": 4000}, {"name": "DEFAULT_BEDROCK_EMBEDDING_MODEL_NAME", "type": "str", "value": ""}], "kinesis": [{"name": "KINESIS_PARTITION_KEY", "type": "str", "value": ""}, {"name": "KINESIS_STREAM_NAME", "type": "str", "value": ""}], "langgraph": [{"name": "RDS_PSQL_USER", "type": "str", "value": "postgress"}, {"name": "RDS_PSQL_ENDPOINT", "type": "str", "value": "rds"}, {"name": "RDS_PSQL_PORT", "type": "str", "value": "5432"}, {"name": "RDS_PSQL_DB", "type": "str", "value": "langgraph_test"}, {"name": "LANGGRAPH_COMPILE_DEBUG", "type": "bool", "value": false}, {"name": "RDS_PSQL_PASSWORD", "type": "str", "value": "genai"}]}}, "native": {"configurations": [], "integrations": {"openai": [{"name": "OPENAI_ENDPOINT", "type": "str", "value": ""}, {"name": "PROXY_NG", "type": "str", "value": ""}, {"name": "OPENAI_KEY", "type": "str", "value": ""}, {"name": "OPENAI_VERSION", "type": "str", "value": ""}, {"name": "TIKTOKEN_CACHE_DIR", "type": "str", "value": "./tiktoken_cache"}, {"name": "DEFAULT_EMBEDDING_MODEL_NAME", "type": "str", "value": "text-embedding-ada-002"}, {"name": "DEFAULT_LLM_MODEL_NAME", "type": "str", "value": "gpt-35-turbo"}, {"name": "INSTRUCTIONS", "type": "str", "value": ""}, {"name": "TEMPERATURE", "type": "float", "value": 0.5}, {"name": "MAX_TOKENS", "type": "int", "value": 4000}, {"name": "OPENAI_TIMEOUT", "type": "int", "value": 60}, {"name": "OPENAI_MAX_RETRIES", "type": "int", "value": 3}], "opensearch": [{"name": "OPENSEARCH_REGION", "type": "str", "value": ""}, {"name": "OPENSEARCH_INDEX", "type": "str", "value": ""}, {"name": "OPENSEARCH_ENDPOINT", "type": "str", "value": ""}, {"name": "OPENSEARCH_TIMEOUT", "type": "int", "value": 60}, {"name": "OPENSEARCH_RETRIES_WAIT", "type": "int", "value": 5}, {"name": "OPENSEARCH_MAX_RETRIES", "type": "int", "value": 3}, {"name": "OPENSEARCH_USER", "type": "str", "value": ""}, {"name": "OPENSEARCH_PASSWORD", "type": "str", "value": ""}], "faiss": [{"name": "FAISS_PATH", "type": "str", "value": "/tmp/faiss/"}, {"name": "FAISS_INDEX", "type": "str", "value": "findex"}, {"name": "FAISS_DIM", "type": "int", "value": 1536}, {"name": "FAISS_M", "type": "int", "value": 32}], "dynamodb": [{"name": "AWS_REGION_NAME", "type": "str", "value": "eu-west-1"}], "bedrock": [{"name": "BEDROCK_MODEL_ID", "type": "str", "value": ""}, {"name": "ALLOW_SYSTEM_PROMPT", "type": "list", "value": []}, {"name": "INSTRUCTIONS", "type": "str", "value": "Toma el comportamiento que te indica el usuario."}, {"name": "TEMPERATURE", "type": "float", "value": 0.5}, {"name": "MAX_TOKENS", "type": "int", "value": 4000}, {"name": "DEFAULT_BEDROCK_EMBEDDING_MODEL_NAME", "type": "str", "value": ""}, {"name": "TIKTOKEN_CACHE_DIR", "type": "str", "value": ""}], "kinesis": [{"name": "KINESIS_PARTITION_KEY", "type": "str", "value": ""}, {"name": "KINESIS_STREAM_NAME", "type": "str", "value": ""}], "langgraph": [{"name": "RDS_PSQL_USER", "type": "str", "value": "postgress"}, {"name": "RDS_PSQL_ENDPOINT", "type": "str", "value": "rds"}, {"name": "RDS_PSQL_PORT", "type": "str", "value": "5432"}, {"name": "RDS_PSQL_DB", "type": "str", "value": "langgraph_test"}, {"name": "LANGGRAPH_COMPILE_DEBUG", "type": "bool", "value": false}, {"name": "RDS_PSQL_PASSWORD", "type": "str", "value": "genai"}]}}, "none": {"configurations": [], "integrations": {"dynamodb": [{"name": "AWS_REGION_NAME", "type": "str", "value": "eu-west-1"}], "kinesis": [{"name": "KINESIS_PARTITION_KEY", "type": "str", "value": ""}, {"name": "KINESIS_STREAM_NAME", "type": "str", "value": ""}], "langgraph": [{"name": "RDS_PSQL_USER", "type": "str", "value": "postgress"}, {"name": "RDS_PSQL_ENDPOINT", "type": "str", "value": "rds"}, {"name": "RDS_PSQL_PORT", "type": "str", "value": "5432"}, {"name": "RDS_PSQL_DB", "type": "str", "value": "langgraph_test"}, {"name": "LANGGRAPH_COMPILE_DEBUG", "type": "bool", "value": false}, {"name": "RDS_PSQL_PASSWORD", "type": "str", "value": "genai"}]}}}, "_instrumentation_available": ["langchain", "openai", "bedrock"], "_template": "https://bitbucket.globaldevtools.bbva.com/bitbucket/scm/genai_viriato/genai-viriato-executelambdarfqs.git", "_commit": "24aefae18b9ef9401698e03c748960d19108db3c"}}, "directory": null}