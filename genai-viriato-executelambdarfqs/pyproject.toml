# Poetry pyproject.toml: https://python-poetry.org/docs/pyproject/

[build-system]
requires = ["poetry_core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "executeLambdaRfqs"
version = "0.1.0"
description = "Converts BBG chats to formatted RFQs"
readme = "README.md"
authors = [
  "BBVA <<EMAIL>>"]
packages = [
    { include = "src" }]

# Keywords description https://python-poetry.org/docs/pyproject/#keywords
keywords = []

# Pypi classifiers: https://pypi.org/classifiers/
classifiers = [
  "Development Status :: 5 - Production/Stable",
  "Intended Audience :: Developers",
  "Operating System :: OS Independent",
  "Topic :: Software Development :: Libraries :: Python Modules",]

[tool.poetry.dependencies]
python = ">=3.11.0,<3.12"
setuptools = "78.1.1"
boto3 = "1.34.162"
botocore = "1.34.162"
jinja2="3.1.6"
pydantic = "2.7.4"
pydantic-settings = "2.6.1"
httpx="0.27.0"

#observability dependencies
genai-libs-observability = {version = "1.5.1", source = "BBVARepo", extras = ["aws_lambda", "openai"]}



#framework dependencies


#integrations dependencies
openai="1.28.1"

[tool.poetry.group.dev.dependencies]
#test
pytest = "8.2.0"
pytest-cov = "4.1.0"
pytest-env="1.0.1"
black = "24.4.2"
isort = "5.13.2"
#TODO docstyle, development styletools
flake8 = "7.0.0"
flake8-pyproject="1.2.3"
pyproject-autoflake="1.0.2"
ruff="0.8.4"
#darglint = "^1.8.1"
#pydocstyle = "^6.0.0"
#pylint = "^2.7.2"
#mypy = "^0.812"
#pyupgrade = "^2.10.0"

#TODO dependency vulnerability scanner
#safety = "^2.3.4"
#bandit = "^1.7.5"

[[tool.poetry.source]]
name = "BBVARepo"
url = "https://artifactory.globaldevtools.bbva.com/artifactory/api/pypi/gl-ai-genai-python-virtual/simple"
priority = "primary"

[tool.black]
target-version = ["py311"]
line-length = 79

[tool.isort]
profile = "black"

[tool.coverage.run]
branch = true
source = ["src"]
command_line = "-m pytest"

[tool.coverage.xml]
output = "coverage.xml"

[tool.coverage.report]
show_missing = true

[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = "-s -v --durations=0 --cov=src --cov-report=term --cov-report=xml --junitxml=xunit-result.xml"
cache_dir = ".cache/pytest_cache"
python_files = ["**/*.py"]
env = ["GENAI_ENVIRONMENT=test","AWS_DEFAULT_REGION=local"]

[tool.flake8]
max-line-length = 79
exclude = [".git",".venv","__pycache__", "old", "build", "dist",
    "./tests/test_infrastructure/test_genai/test_persistence/test_persistence_unit_of_work.py",
    "./tests/test_lambda_funcion.py"]
ignore = ["DAR101", "DAR103", "DAR402", "DAR401", "DAR201", "DAR102", "DAR003", "DAR301", "E999", "W293", "W391",
    "E203","W503"]

[tool.autoflake]
in-place = true
recursive = true
remove-all-unused-imports = true
exclude = ["./config/", "./src/infraestructure/core/", "./tests/test_lambda_funcion.py"]


