FROM artifactory.globaldevtools.bbva.com:443/public.ecr.aws-remote/lambda/python:3.11

ARG ARTIFACTORY_USER \
    ARTIFACTORY_PASSWORD \
    GENAI_ENVIRONMENT

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    GENAI_ENVIRONMENT=${GENAI_ENVIRONMENT:-local} \
    APP_DIR=${LAMBDA_TASK_ROOT:-/var/task} \
    POETRY_VIRTUALENVS_IN_PROJECT=false \
    POETRY_WARNINGS_EXPORT=false \
    POETRY_VERSION=1.8.5 \
    POETRY_NO_INTERACTION=1

WORKDIR ${APP_DIR}

COPY ./pyproject.toml/ ./poetry.lock ./collector_local.yaml/ ${APP_DIR}/
COPY ./config/${GENAI_ENVIRONMENT}/config.cfg  ${APP_DIR}/config/${GENAI_ENVIRONMENT}/config.cfg
COPY ./resources ${APP_DIR}/resources
COPY ./src ${APP_DIR}/src

RUN pip install --index-url https://${ARTIFACTORY_USER}:${ARTIFACTORY_PASSWORD}@artifactory.globaldevtools.bbva.com/artifactory/api/pypi/gl-ai-genai-python-virtual/simple --no-cache-dir poetry==${POETRY_VERSION} && \
    poetry config http-basic.BBVARepo ${ARTIFACTORY_USER} ${ARTIFACTORY_PASSWORD}
ADD https://${ARTIFACTORY_USER}:${ARTIFACTORY_PASSWORD}@artifactory.globaldevtools.bbva.com:443/artifactory/gl-ai-genai-python-virtual/genai/genai_libs_otel_lambda_layer/1.26.0/otel-lambda-layer.zip /tmp/otel-lambda-layer.zip
RUN poetry config virtualenvs.create false && \
    poetry install --without dev --no-root && \
    python -m zipfile -e /tmp/otel-lambda-layer.zip /opt && \
    chmod -R 755 /opt && \
    rm -rf /tmp/otel-lambda-layer.zip /root/.cache/* && \
    rm -rf /var/lang/lib/python*/site-packages/pip* /var/lang/lib/python*/site-packages/setuptools* /var/lang/bin/pip*

ADD src ${APP_DIR}

RUN chown -R nobody:nobody ${APP_DIR} && chmod -R 755 ${APP_DIR}
USER nobody

CMD [ "lambda_function.handler" ]