import json
import logging

from genai.libs.observability.genai_lambda import trace_lambda

from src.infrastructure.core.config import genai_config

# please do not remove, this preconfigures opentelemetry!!


default_dimensions = {
    "Lambda": genai_config.GENAI_NAME,
    "Application": genai_config.GENAI_APPLICATION,
    "Product": genai_config.GENAI_PRODUCT,
    "service.name": genai_config.GENAI_NAME,
}


logger = logging.getLogger(__name__)


# Read and Process event
def process_event(event):

    event = json.loads(event["body"]) if "body" in event else event
    return event


# Lambda handler
@trace_lambda(
    environment=genai_config.GENAI_ENVIRONMENT,
    default_dimensions=default_dimensions,
)
def handler(event, context):
    logger.info("Processing event...")

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps({"response": "Hello, World!"}),
    }
