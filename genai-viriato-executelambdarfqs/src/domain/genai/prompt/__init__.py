from __future__ import annotations

from abc import ABC, abstractmethod

from genai.libs.observability import trace_execution


class PromptRepository(ABC):

    def __enter__(self) -> PromptRepository:
        return self

    def __exit__(self, *args):
        self.close()

    @abstractmethod
    @trace_execution
    def get_prompt(self, template, template_parameters: dict = None):
        pass

    @abstractmethod
    def close(self):
        pass
