from __future__ import annotations

import abc
from typing import Any

from genai.libs.observability import trace_execution


class AbstractEmbeddingUnitOfWork(abc.ABC):
    _embedding: Any

    def __enter__(self) -> AbstractEmbeddingUnitOfWork:
        return self

    def __exit__(self, *args):
        """Subclasses should implement any necessary cleanup logic here."""

    @abc.abstractmethod
    def get(self):
        raise NotImplementedError

    @abc.abstractmethod
    @trace_execution
    def execute_embedding(
        self, texts: list[str], batch_size: int = 16, input_model_name=None
    ) -> tuple[list[list[float]], int]:
        raise NotImplementedError
