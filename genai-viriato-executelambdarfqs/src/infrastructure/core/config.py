import logging
import os
from typing import Optional

import boto3
from botocore.exceptions import ClientError
from pydantic import ConfigDict
from pydantic_settings import BaseSettings

logger = logging.getLogger(__name__)


class Config(BaseSettings):

    COMPONENT_NAME: str = "executeLambdaRfqs"

    COMPONENT_DESCRIPTION: str = "Converts BBG chats to formatted RFQs"

    COMPONENT_VERSION: str = "0.1.0"

    GENAI_ENVIRONMENT: str = "local"

    PROMPT_REPOSITORY_ROUTE: str = "./resources/templates/"

    S3_BUCKET: str = ""

    GENAI_NAME: str = "viriato-orq-executeLambdaRfqs"

    GENAI_APPLICATION: str = "viriato"

    GENAI_PRODUCT: str = "viriato"

    PARAMETER_STORE_CONFIG: str = ""

    GENAI_MIDDLEWARE_HEADERS: str = (
        "x-amzn-trace-id,x-rho-traceid,x-rho-parentspanid"
    )

    GENAI_OTEL_EXPORTER_KINESIS_STREAM: Optional[str] = None

    GENAI_OTEL_EXPORTER_KINESIS_STREAM_OBFUSCATE_FIELDS: Optional[str] = None

    GENAI_OTEL_EXPORTER_OTLP_STREAM_OBFUSCATE_FIELDS: Optional[str] = None

    DEFAULT_LOGGING_LEVEL: str = "INFO"

    # framework configurations

    # integrations configurations
    OPENAI_ENDPOINT: str = ""

    PROXY_NG: str = ""

    OPENAI_KEY: str = ""

    OPENAI_VERSION: str = ""

    TIKTOKEN_CACHE_DIR: str = "./tiktoken_cache"

    DEFAULT_EMBEDDING_MODEL_NAME: str = "text-embedding-ada-002"

    DEFAULT_LLM_MODEL_NAME: str = "gpt-35-turbo"

    INSTRUCTIONS: str = ""

    TEMPERATURE: float = 0.5

    MAX_TOKENS: int = 4000

    OPENAI_TIMEOUT: int = 60

    OPENAI_MAX_RETRIES: int = 3

    model_config = ConfigDict(extra="ignore")

    def model_post_init(self, __context):
        parameter_store_path = self.model_dump().get(
            "PARAMETER_STORE_CONFIG", ""
        )
        parameters = {}
        if (
            parameter_store_path is not None
            and parameter_store_path.strip() != ""
        ):
            parameters = self._get_parameters_from_parameter_store(
                parameter_store_path
            )
        for attribute, value in self.model_dump().items():
            if attribute in parameters:
                attribute_type = type(getattr(self, attribute))
                value = parameters[attribute]
                setattr(self, attribute, attribute_type(value))
                logger.info(
                    f"se ha cambiado el atributo {attribute} al valor {value}"
                )
            if isinstance(value, str) and value.startswith(
                "arn:aws:secretsmanager"
            ):
                setattr(
                    self, attribute, self._get_value_from_secret_manager(value)
                )

    @staticmethod
    def _get_parameters_from_parameter_store(parameter_store_config):
        # Create a Secrets Manager client
        session = boto3.session.Session()
        client = session.client(service_name="ssm")
        parameters_result = {}
        try:
            get_parameters = client.get_parameter(
                Name=parameter_store_config, WithDecryption=False
            )
        except ClientError as e:
            # For a list of exceptions thrown, see
            # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
            raise e
        parameters = get_parameters["Parameter"]["Value"]
        lines = parameters.splitlines()
        for line in lines:
            # Ignore empty lines
            if line.strip():
                # split line caused by =
                key, value = line.split("=", 1)
                parameters_result[key] = value
        return parameters_result

    @staticmethod
    def _get_value_from_secret_manager(secret_arn):
        # Create a Secrets Manager client
        session = boto3.session.Session()
        client = session.client(service_name="secretsmanager")
        try:
            get_secret_value_response = client.get_secret_value(
                SecretId=secret_arn
            )
        except ClientError as e:
            # For a list of exceptions thrown, see
            # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
            raise e
        secret = get_secret_value_response["SecretString"]
        return secret


genai_config = Config(
    _env_file=[
        "./.env",
        os.path.expandvars("./config/${GENAI_ENVIRONMENT}/config.cfg"),
    ]
)
