import logging

import httpx as _httpx
from openai.lib.azure import AzureOpenAI

from src.domain.genai.llm import AbstractLlmUnitOfWork
from src.infrastructure.core.config import genai_config

log = logging.getLogger(__name__)


def _get_http_client(proxy=genai_config.PROXY_NG):
    return _httpx.Client(proxies=proxy)


class OpenAILlmUnitOfWork(AbstractLlmUnitOfWork):
    def __init__(
        self,
        model_name=genai_config.DEFAULT_LLM_MODEL_NAME,
        http_client=_get_http_client,
        azure_endpoint=genai_config.OPENAI_ENDPOINT,
        azure_api_key=genai_config.OPENAI_KEY,
        azure_api_version=genai_config.OPENAI_VERSION,
    ):
        self._model_name = model_name
        self._http_client = http_client
        self._azure_endpoint = azure_endpoint
        self._azure_api_key = azure_api_key
        self._azure_api_version = azure_api_version

    def __enter__(self):
        # Retry on the following status errors:
        #   Connection errors (for example,
        #       due to a network connectivity problem)
        #   408 Request Timeout
        #   409 Conflict
        #   429 Rate Limit
        #   >=500 Internal errors

        self._model = AzureOpenAI(
            azure_endpoint=self._azure_endpoint,
            api_key=self._azure_api_key,
            api_version=self._azure_api_version,
            http_client=self._http_client(),
            timeout=int(genai_config.OPENAI_TIMEOUT),  # seconds
            max_retries=int(genai_config.OPENAI_MAX_RETRIES),
        )

        return super().__enter__()

    def get(self):
        return self._model

    def execute_prompt(
        self,
        prompt,
        model=None,
        instructions=genai_config.INSTRUCTIONS,
        temperature=genai_config.TEMPERATURE,
        max_tokens=genai_config.MAX_TOKENS,
    ):
        # User prompt
        messages = [
            {
                "role": "user",
                "content": prompt,
            }
        ]

        # System prompt
        if instructions:
            messages.insert(
                0,
                {
                    "role": "system",
                    "content": instructions,
                },
            )

        response = self._model.chat.completions.create(
            model=model if model else self._model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
        )

        if response.choices[0].message.content:
            return response.choices[0].message.content
