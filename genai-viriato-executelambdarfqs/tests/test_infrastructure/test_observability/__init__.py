import time
from unittest.mock import Magic<PERSON>ock

import pytest

from src.infrastructure.observability import trace_execution


def simple_function(x, y):
    return x + y


# Fixtures para los mocks
@pytest.fixture
def mock_log(monkeypatch):
    mock = MagicMock()
    monkeypatch.setattr("src.infrastructure.observability.log", mock)
    return mock


@pytest.fixture
def mock_warn(monkeypatch):
    mock = MagicMock()
    monkeypatch.setattr("src.infrastructure.observability.warnings.warn", mock)
    return mock


def test_logs_entry_and_exit(mock_log, mock_warn):
    decorated_func = trace_execution(simple_function)
    result = decorated_func(5, 3)

    assert result == 8
    mock_log.info.assert_any_call("Entering simple_function")
    exit_call_args = mock_log.info.call_args_list[1][0][0]
    assert "Exiting  simple_function" in exit_call_args
    assert "with result: 8" in exit_call_args


def test_preserves_original_function_result(mock_warn):
    decorated_func = trace_execution(simple_function)
    result = decorated_func(10, 20)
    assert result == 30


def test_measures_execution_time_correctly(mock_log, mock_warn):
    def slow_function():
        time.sleep(0.1)
        return "done"

    decorated_func = trace_execution(slow_function)
    result = decorated_func()

    assert result == "done"
    exit_call_args = mock_log.info.call_args_list[1][0][0]
    assert "elapsed time: " in exit_call_args
    elapsed_time = float(exit_call_args.split("elapsed time: ")[1].split()[0])
    assert elapsed_time >= 0.1


def test_preserves_function_metadata(mock_warn):
    @trace_execution
    def function_with_docstring():
        """This is a test docstring"""
        return True

    assert function_with_docstring.__name__ == "function_with_docstring"
    assert function_with_docstring.__doc__ == "This is a test docstring"


def test_handles_exceptions_correctly(mock_log, mock_warn):
    @trace_execution
    def function_that_raises():
        raise ValueError("Test exception")

    with pytest.raises(ValueError, match="Test exception"):
        function_that_raises()

    mock_log.info.assert_any_call("Entering function_that_raises")


def test_shows_deprecation_warning(mock_warn):
    decorated_func = trace_execution(simple_function)
    decorated_func(1, 2)

    mock_warn.assert_called_once()
    args, kwargs = mock_warn.call_args
    assert "trace_execution" in args[0]
    assert "genai.libs.observability" in args[0]
    assert args[1] == DeprecationWarning
    assert kwargs["stacklevel"] == 2
