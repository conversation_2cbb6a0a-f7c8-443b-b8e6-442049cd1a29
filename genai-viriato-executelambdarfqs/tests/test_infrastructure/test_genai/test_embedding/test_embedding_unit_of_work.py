import unittest
from unittest.mock import MagicMock, call, patch

import src.infrastructure.genai.embedding as genai_embedding


class TestOpenAIEmbeddingUnitOfWork(unittest.TestCase):

    @patch("src.infrastructure.genai.embedding._httpx.Client")
    def test_get_http_client(self, mock_http_client):
        mock_http_client.return_value = "http_client"

        proxy = "***************************:port2"
        result = genai_embedding._get_http_client(proxy=proxy)

        self.assertEqual(result, "http_client")
        mock_http_client.assert_called_once_with(proxies=proxy)

    @patch("src.infrastructure.genai.embedding.os.environ", new_callable=dict)
    @patch("src.infrastructure.genai.embedding._get_http_client")
    @patch("src.infrastructure.genai.embedding._httpx.Client")
    @patch("src.infrastructure.genai.embedding.AzureOpenAI")
    def test_openai_embedding_unit_of_work_default_params(
        self, mock_embed, mock_httpx_client, mock_get_http_client, mock_environ
    ):
        mock_embed.return_value = MagicMock()
        mock_httpx_client.return_value = MagicMock()
        mock_get_http_client = mock_httpx_client.return_value

        with genai_embedding.OpenAIEmbeddingUnitOfWork() as uow:
            self.assertEqual(
                uow._model,
                genai_embedding.genai_config.DEFAULT_EMBEDDING_MODEL_NAME,
            )
            self.assertIs(uow._http_client(), mock_get_http_client)
            self.assertEqual(uow._proxy, genai_embedding.genai_config.PROXY_NG)
            self.assertEqual(
                uow._azure_endpoint,
                genai_embedding.genai_config.OPENAI_ENDPOINT,
            )
            self.assertEqual(
                uow._azure_apikey, genai_embedding.genai_config.OPENAI_KEY
            )
            self.assertEqual(
                uow._azure_api_version,
                genai_embedding.genai_config.OPENAI_VERSION,
            )
            self.assertEqual(
                mock_environ["TIKTOKEN_CACHE_DIR"],
                genai_embedding.genai_config.TIKTOKEN_CACHE_DIR,
            )

            self.assertIsNotNone(uow.get())
            mock_embed.assert_called_once_with(
                azure_endpoint=uow._azure_endpoint,
                api_key=uow._azure_apikey,
                api_version=uow._azure_api_version,
                http_client=uow._http_client(),
                timeout=int(
                    genai_embedding.genai_config.OPENAI_TIMEOUT
                ),  # seconds
                max_retries=int(
                    genai_embedding.genai_config.OPENAI_MAX_RETRIES
                ),
            )

            texts = ["Test text", "Test text 2"]
            uow.execute_embedding(texts)
            mock_embed.return_value.embeddings.create.assert_called_once_with(
                model=uow._model,
                input=texts,
            )

    @patch("src.infrastructure.genai.embedding.os.environ", new_callable=dict)
    @patch("src.infrastructure.genai.embedding._get_http_client")
    @patch("src.infrastructure.genai.embedding.AzureOpenAI")
    def test_openai_embedding_unit_of_work_custom_params(
        self,
        mock_embed,
        mock_get_http_client,
        mock_environ,
    ):

        mock_response = MagicMock()
        mock_response.data = []  # no values needed for this test
        mock_embed.return_value = MagicMock()
        mock_embed.return_value.embeddings.create.return_value = mock_response

        model = "test_model2"
        http_client = mock_get_http_client
        proxy = "***************************:port2"
        azure_endpoint = "http://<openaiendpointurl2>"
        azure_apikey = "<openaikey2>"
        azure_api_version = "<openaiversion2>"

        with genai_embedding.OpenAIEmbeddingUnitOfWork(
            model=model,
            http_client=http_client,
            proxy=proxy,
            azure_endpoint=azure_endpoint,
            azure_apikey=azure_apikey,
            azure_api_version=azure_api_version,
        ) as uow:
            self.assertEqual(uow._model, model)
            self.assertEqual(uow._http_client, http_client)
            self.assertEqual(uow._proxy, proxy)
            self.assertEqual(uow._azure_endpoint, azure_endpoint)
            self.assertEqual(uow._azure_apikey, azure_apikey)
            self.assertEqual(uow._azure_api_version, azure_api_version)
            self.assertEqual(
                mock_environ["TIKTOKEN_CACHE_DIR"],
                genai_embedding.genai_config.TIKTOKEN_CACHE_DIR,
            )

            self.assertIsNotNone(uow.get())
            mock_embed.assert_called_once_with(
                azure_endpoint=uow._azure_endpoint,
                api_key=uow._azure_apikey,
                api_version=uow._azure_api_version,
                http_client=uow._http_client(),
                timeout=int(genai_embedding.genai_config.OPENAI_TIMEOUT),
                max_retries=int(
                    genai_embedding.genai_config.OPENAI_MAX_RETRIES
                ),
            )
            text1 = "Test text"
            text2 = "Test text 2"
            texts = [text1, text2]
            custom_model = "test_model3"
            batch_size = 1
            uow.execute_embedding(texts, batch_size, custom_model)
            mock_embed.return_value.embeddings.create.assert_has_calls(
                [
                    call(input=[text1], model=custom_model),
                    call(input=[text2], model=custom_model),
                ]
            )
