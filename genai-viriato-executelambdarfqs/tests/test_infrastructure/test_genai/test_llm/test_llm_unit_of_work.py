import unittest
from unittest.mock import MagicMock, patch

import src.infrastructure.genai.llm as genai_llm


class TestOpenAILlmUnitOfWork(unittest.TestCase):

    @patch("src.infrastructure.genai.llm._httpx.Client")
    def test_get_http_client(self, mock_http_client):
        mock_http_client.return_value = "http_client"

        proxy = "***************************:port2"
        result = genai_llm._get_http_client(proxy=proxy)

        self.assertEqual(result, "http_client")
        mock_http_client.assert_called_once_with(proxies=proxy)

    @patch("src.infrastructure.genai.llm._get_http_client")
    @patch("src.infrastructure.genai.llm._httpx.Client")
    @patch("src.infrastructure.genai.llm.AzureOpenAI")
    def test_openai_llm_unit_of_work_default_params(
        self, m_llm, mock_httpx_client, mock_get_http_client
    ):
        m_llm.return_value = MagicMock()
        mock_httpx_client.return_value = MagicMock()
        mock_get_http_client = mock_httpx_client.return_value

        with genai_llm.OpenAILlmUnitOfWork() as uow:
            self.assertEqual(
                uow._model_name, genai_llm.genai_config.DEFAULT_LLM_MODEL_NAME
            )
            self.assertIs(uow._http_client(), mock_get_http_client)
            self.assertEqual(
                uow._azure_endpoint, genai_llm.genai_config.OPENAI_ENDPOINT
            )
            self.assertEqual(
                uow._azure_api_key, genai_llm.genai_config.OPENAI_KEY
            )
            self.assertEqual(
                uow._azure_api_version, genai_llm.genai_config.OPENAI_VERSION
            )

            self.assertIsNotNone(uow.get())
            m_llm.assert_called_once_with(
                azure_endpoint=uow._azure_endpoint,
                api_key=uow._azure_api_key,
                api_version=uow._azure_api_version,
                http_client=uow._http_client(),
                timeout=int(genai_llm.genai_config.OPENAI_TIMEOUT),  # seconds
                max_retries=int(genai_llm.genai_config.OPENAI_MAX_RETRIES),
            )

            prompt = "Test prompt"
            messages = (
                [
                    {
                        "role": "system",
                        "content": "Toma el comportamiento "
                        "que te indica el usuario.",
                    }
                ]
                if genai_llm.genai_config.INSTRUCTIONS
                else []
            )
            messages.append({"role": "user", "content": prompt})
            uow.execute_prompt(prompt)
            m_llm.return_value.chat.completions.create.assert_called_once_with(
                model=uow._model_name,
                temperature=genai_llm.genai_config.TEMPERATURE,
                max_tokens=genai_llm.genai_config.MAX_TOKENS,
                messages=messages,
            )

    @patch("src.infrastructure.genai.llm._get_http_client")
    @patch("src.infrastructure.genai.llm.AzureOpenAI")
    def test_openai_llm_unit_of_work_custom_params(
        self, m_llm, mock_get_http_client
    ):
        m_llm.return_value = MagicMock()

        model = "test_model2"
        http_client = mock_get_http_client
        azure_endpoint = "http://<openaiendpointurl2>"
        azure_api_key = "<openaikey2>"
        azure_api_version = "<openaiversion2>"

        with genai_llm.OpenAILlmUnitOfWork(
            model_name=model,
            http_client=http_client,
            azure_endpoint=azure_endpoint,
            azure_api_key=azure_api_key,
            azure_api_version=azure_api_version,
        ) as uow:
            self.assertEqual(uow._model_name, model)
            self.assertEqual(uow._http_client, http_client)
            self.assertEqual(uow._azure_endpoint, azure_endpoint)
            self.assertEqual(uow._azure_api_key, azure_api_key)
            self.assertEqual(uow._azure_api_version, azure_api_version)

            self.assertIsNotNone(uow.get())
            m_llm.assert_called_once_with(
                azure_endpoint=uow._azure_endpoint,
                api_key=uow._azure_api_key,
                api_version=uow._azure_api_version,
                http_client=uow._http_client(),
                timeout=int(genai_llm.genai_config.OPENAI_TIMEOUT),  # seconds
                max_retries=int(genai_llm.genai_config.OPENAI_MAX_RETRIES),
            )

            prompt = "Test prompt"
            new_model = "test_model3"
            instructions = False
            temperature = 0.43
            max_tokens = 123
            uow.execute_prompt(
                prompt,
                model=new_model,
                instructions=instructions,
                temperature=temperature,
                max_tokens=max_tokens,
            )
            m_llm.return_value.chat.completions.create.assert_called_once_with(
                model=new_model,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=[{"role": "user", "content": prompt}],
            )
