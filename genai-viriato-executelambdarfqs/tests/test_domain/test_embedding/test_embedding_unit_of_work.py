import pytest

from src.domain.genai.embedding import AbstractEmbeddingUnitOfWork


def test_cannot_instantiate_abstract_class():
    """Ensure instantiating AbstractLlmUnitOfWork
    raises TypeError."""

    with pytest.raises(TypeError):
        AbstractEmbeddingUnitOfWork()


def test_enter_exit_methods():
    """Ensure a concrete implementation can be used as a
    context manager."""

    class MockEmbeddingUnitOfWork(AbstractEmbeddingUnitOfWork):
        def execute_embedding(
            self, texts: list[str], batch_size: int = 16, input_model_name=None
        ) -> tuple[list[list[float]], int]:
            pass

        def get(self):
            pass

    with MockEmbeddingUnitOfWork() as uow:
        assert uow is not None


def test_get_method_not_implemented():
    """Ensure a subclass without get method raises TypeError."""

    class IncompleteEmbeddingUnitOfWork(AbstractEmbeddingUnitOfWork):
        pass

    with pytest.raises(TypeError):
        IncompleteEmbeddingUnitOfWork()


def test_get_method_is_called():
    """Ensure concrete implementation returns expected result."""

    class ConcreteEmbeddingUnitOfWork(AbstractEmbeddingUnitOfWork):
        def get(self):
            self._embedding = "test"
            return self._embedding

        def execute_embedding(
            self, texts: list[str], batch_size: int = 16, input_model_name=None
        ) -> tuple[list[list[float]], int]:
            pass

    uow = ConcreteEmbeddingUnitOfWork()
    assert uow.get() == "test"
