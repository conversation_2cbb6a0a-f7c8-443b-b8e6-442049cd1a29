import os
from unittest.mock import patch

import pytest

os.environ["AWS_DEFAULT_REGION"] = "eu-west-1"

patch(
    "genai.libs.observability.genai_lambda.trace_lambda",
    lambda environment, default_dimensions: lambda x: x,
).start()

from src.lambda_function import handler


class MockContext:
    def __init__(self):
        self.invoked_function_arn = (
            "arn:aws:lambda:us-west-2:123456789012:function:my-function"
        )
        self.aws_request_id = "request-id-123"


def test_handler_basic_response():
    """Test básico que verifica que el handler responde sin errores"""
    # Evento de prueba genérico
    test_event = {"test": "data"}
    context = MockContext()
    # Ejecutar handler
    handler(test_event, context)

    # Comprobar que hay una respuesta (verificación básica)
    assert True
