import pytest

from src.domain.genai.filesystem import AbstractFileSystemUnitOfWork


def test_cannot_instantiate_abstract_class():
    """Ensure instantiating AbstractLlmUnitOfWork raises TypeError."""
    with pytest.raises(TypeError):
        AbstractFileSystemUnitOfWork()


def test_method_not_implemented():
    """Ensure a subclass missing any of the required
    methods raises TypeError."""

    class IncompleteFileSystemUnitOfWork(AbstractFileSystemUnitOfWork):
        pass

    with pytest.raises(TypeError):
        IncompleteFileSystemUnitOfWork()


def test_method_is_called():
    """Ensure concrete implementation returns expected result."""

    class ConcreteFileSystemUnitOfWork(AbstractFileSystemUnitOfWork):

        def get_file(self, file) -> bytes:
            pass

        def upload_file(self, file_route: str, name: str = None) -> bool:
            pass

        def create_text_file(
            self, text: str, file: str, encoding: str = "utf-8"
        ) -> bool:
            pass

        def list_folder(self, prefix: str) -> list:
            pass

        def move_file(
            self, old_file: str, new_file: str, new_route: str = None
        ) -> bool:
            pass

        def delete_file(self, file: str) -> bool:
            pass

        def move_route(self, route: str):
            self._route = route

    uow = ConcreteFileSystemUnitOfWork()
    route = "test"
    uow.move_route(route=route)
    assert uow._route == route
