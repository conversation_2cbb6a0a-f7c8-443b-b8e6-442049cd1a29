import pytest

from src.domain.genai.persistence import AbstractPersistenceUnitOfWork


def test_cannot_instantiate_abstract_class():
    """Ensure instantiating AbstractLlmUnitOfWork raises TypeError."""
    with pytest.raises(TypeError):
        AbstractPersistenceUnitOfWork()


def test_enter_exit_methods():
    """Ensure a concrete implementation can be used as a context manager."""

    class MockPersistenceUnitOfWork(AbstractPersistenceUnitOfWork):

        def get_connection(self):
            pass

        def close(self):
            pass

    with MockPersistenceUnitOfWork() as uow:
        assert uow is not None


def test_get_method_not_implemented():
    """Ensure a subclass without get method raises TypeError."""

    class IncompletePersistenceUnitOfWork(AbstractPersistenceUnitOfWork):
        pass

    with pytest.raises(TypeError):
        IncompletePersistenceUnitOfWork()


def test_get_method_is_called():
    """Ensure concrete implementation returns expected result."""

    class ConcretePersistenceUnitOfWork(AbstractPersistenceUnitOfWork):

        def get_connection(self):
            self._storage_connection = "test"
            return self._storage_connection

        def close(self):
            pass

    uow = ConcretePersistenceUnitOfWork()
    assert uow.get_connection() == "test"
