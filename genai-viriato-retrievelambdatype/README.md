# viriato

- **Versión**  0.1.0

- **Por:** AA&AT

- **Contacto:** <EMAIL>

Classifies the request and returns an ID of the lambda to call next

## Pre-requisitos

- Docker/Podman
- Python 3.11

## <PERSON><PERSON><PERSON> Empezar

Cree un entorno virtual e instale todas las dependencias:

```bash
make install
```

Inicialice el entorno virtual creado:

```bash
make shell
```

Liste los comandos disponibles:

```bash
make help
```

# Desarrollo en local

## Instalación y Ejecución

Puede levantar un contenedor en local para probar la aplicación con el comando:

```bash
make docker-build
make docker-run
```

## Pruebas

Sin parámetros:

```bash
curl -d "{}" -X POST http://localhost:9000/2015-03-31/functions/function/invocations
```

Con parámetros:

```bash
curl -d '{"payload":"hello world!"}' -X POST http://localhost:9000/2015-03-31/functions/function/invocations
```
