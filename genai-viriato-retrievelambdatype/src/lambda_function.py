import json
import logging
import os
import requests

from genai.libs.observability.genai_lambda import trace_lambda
from src.infrastructure.core.config import genai_config
from openai import AzureOpenAI

# please do not remove, this preconfigures opentelemetry!!
default_dimensions = {
    "Lambda": genai_config.GENAI_NAME,
    "Application": genai_config.GENAI_APPLICATION,
    "Product": genai_config.GENAI_PRODUCT,
    "service.name": genai_config.GENAI_NAME,
}

# Configuración del logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Configurar Proxy
PROXY_URL = "http://ngpw10454:<EMAIL>:8080"
proxies = {"http": PROXY_URL, "https": PROXY_URL}

# Configurar requests para que use el proxy globalmente
os.environ["HTTP_PROXY"] = PROXY_URL
os.environ["HTTPS_PROXY"] = PROXY_URL

# Verify proxy connection
try:
    url = "https://viriatollmservice.openai.azure.com/"
    response = requests.get(url, proxies=proxies, timeout=5)
    logger.info(f"Conexión al proxy exitosa: {response.status_code}")
except Exception as e:
    logger.error(f"Error proxy connection: {str(e)}")

# Inicializar el modelo de Azure OpenAI
client = AzureOpenAI(
  azure_endpoint="https://viriatollmservice.openai.azure.com",  # "https://viriato-test-openai.openai.azure.com",
  api_key="gYFNmLJQIci8SzaGaUGnAwCBPrU9BAKxpAR9JsbEkAGY1Z4D9U3PJQQJ99BBACR0EKYXJ3w3AAABACOGGeHN",  # "b3d7c594b8c14e728e242f79ab43402e",
  api_version="2024-10-21",
  timeout=120,  # 2 minutes in seconds
  max_retries=3
)
with open('../resources/commun_concepts.txt', 'r') as file:
    commun_concepts = file.read().strip()

with open('../resources/prompt_master.txt', 'r') as file:
    prompt_master = file.read().strip()

with open('../resources/prompts_sql_db.txt', 'r') as file:
    prompt_db = file.read().strip()


# Classify workflow
def classify_workflow(client, prompt, commun_concepts, prompt_db, natural_language):
    combined_prompt = commun_concepts + "\n" + prompt

    try:
        response = client.chat.completions.create(
            model="viriato-gpt-4o",
            messages=[
                {"role": "system", "content": combined_prompt},
                {"role": "user", "content": natural_language}
            ],
            max_tokens=100 
        )

        classification = response.choices[0].message.content.strip()
        print(classification)
        # Parse answer
        if classification.startswith(("1", "2")):
            task_type = int(classification[0])
            explanation = None
        elif classification.startswith(("3", "4")):
            parts = classification.split(":", 1)
            task_type = int(parts[0])
            explanation = parts[1].strip() if len(parts) > 1 else None
        else:
            task_type = 3  # Default value for incorrect answers
            explanation = classification.strip()

        result = {
            "message": natural_language,
            "taskType": task_type,
            "explanation": explanation
        }

    except Exception as e:
        raise Exception("Error during classification: {str(e)}")

    return json.dumps(result)


# Lambda handler
@trace_lambda(
    environment=genai_config.GENAI_ENVIRONMENT,
    default_dimensions=default_dimensions,
)
def handler(event, context):
    """
    AWS Lambda invoking Azure OpenAI to classify user request.
    """
    natural_language = event.get("message")

    if not natural_language:
        raise Exception("Error: No message provided")

    try:
        # Assuming that "prompt_master" and "commun_concepts" are globally defined
        result_classification_json = classify_workflow(client, prompt_master, commun_concepts, prompt_db, natural_language)
        result_classification = json.loads(result_classification_json)

        task_type = result_classification.get("taskType")
        explanation = result_classification.get("explanation")
        print('expl')
        print(explanation)
        if task_type == 3:
            msg = explanation or 'The question is off-topic and cannot be answered.'
        elif task_type == 4:
            msg = explanation or 'Additional information is required to respond to your request.'
        elif task_type in (1, 2):
            msg = result_classification.get("message")
        else:
            # If task_type is None or unexpected
            msg = explanation or 'Unable to classify the request properly.'

        return {
            "message": msg,
            "taskType": task_type
        }

    except Exception as e:
        raise Exception("Error: {str(e)}")
