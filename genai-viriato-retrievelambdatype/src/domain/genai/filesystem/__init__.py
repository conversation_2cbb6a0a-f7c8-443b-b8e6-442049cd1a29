import abc

from genai.libs.observability import trace_execution


class AbstractFileSystemUnitOfWork(abc.ABC):
    _route: str

    @trace_execution
    @abc.abstractmethod
    def get_file(self, file) -> bytes:
        raise NotImplementedError

    @trace_execution
    @abc.abstractmethod
    def upload_file(self, file_route: str, name: str = None) -> bool:
        raise NotImplementedError

    @trace_execution
    @abc.abstractmethod
    def create_text_file(
        self, text: str, file: str, encoding: str = "utf-8"
    ) -> bool:
        raise NotImplementedError

    @trace_execution
    @abc.abstractmethod
    def list_folder(self, prefix: str) -> list:
        raise NotImplementedError

    @trace_execution
    @abc.abstractmethod
    def move_file(
        self, old_file: str, new_file: str, new_route: str = None
    ) -> bool:
        raise NotImplementedError

    @trace_execution
    @abc.abstractmethod
    def delete_file(self, file: str) -> bool:
        raise NotImplementedError

    @trace_execution
    @abc.abstractmethod
    def move_route(self, route):
        raise NotImplementedError
